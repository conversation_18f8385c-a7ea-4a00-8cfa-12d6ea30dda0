/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./public/index.html",
  ],
  theme: {
    extend: {
      colors: {
        mariner: {
          50: '#eefaff',
          100: '#d8f3ff',
          200: '#bae9ff',
          300: '#8adeff',
          400: '#53caff',
          500: '#2bacff',
          600: '#1490fc',
          700: '#0d74e3',
          800: '#125fbb',
          900: '#155293',
          950: '#123259',
        },
        // Alias per compatibilità con il sistema esistente
        primary: {
          50: '#eefaff',
          100: '#d8f3ff',
          200: '#bae9ff',
          300: '#8adeff',
          400: '#53caff',
          500: '#2bacff',
          600: '#1490fc',
          700: '#0d74e3',
          800: '#125fbb',
          900: '#155293',
          950: '#123259',
        },
      },
      // Configurazioni aggiuntive per il sistema
      backgroundColor: {
        'rest': '#f5f7fa',
        'hover': 'rgba(20, 144, 252, 0.1)', // mariner-600 con opacità
      },
      borderColor: {
        'default': '#e0e0e0',
      },
      textColor: {
        'primary': '#212121',
        'secondary': '#757575',
      },
    },
  },
  plugins: [],
}
