import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Checkbox,
  FormControlLabel
} from '@mui/material';
import {
  Save as SaveIcon,
  Cancel as CancelIcon,
  Link as LinkIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import comandeService from '../../services/comandeService';

const CollegamentiDialog = ({
  open,
  onClose,
  comanda,
  onSuccess
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [cavi, setCavi] = useState([]);
  const [caviSelezionati, setCaviSelezionati] = useState({});

  useEffect(() => {
    if (open && comanda) {
      loadCaviComanda();
    }
  }, [open, comanda]);

  const loadCaviComanda = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const caviData = await comandeService.getCaviComanda(comanda.codice_comanda);
      setCavi(caviData);
      
      // Inizializza la selezione (tutti i cavi selezionati di default)
      const initialSelection = {};
      caviData.forEach(cavo => {
        initialSelection[cavo.id_cavo] = true;
      });
      setCaviSelezionati(initialSelection);
      
    } catch (err) {
      console.error('Errore nel caricamento cavi:', err);
      setError('Errore nel caricamento dei cavi della comanda');
    } finally {
      setLoading(false);
    }
  };

  const handleCavoToggle = (idCavo) => {
    setCaviSelezionati(prev => ({
      ...prev,
      [idCavo]: !prev[idCavo]
    }));
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      setError(null);

      // Prepara i dati di collegamento per i cavi selezionati
      const datiCollegamento = {};
      Object.keys(caviSelezionati).forEach(idCavo => {
        if (caviSelezionati[idCavo]) {
          datiCollegamento[idCavo] = {
            responsabile: comanda.responsabile || '',
            data_collegamento: new Date().toISOString().split('T')[0]
          };
        }
      });

      if (Object.keys(datiCollegamento).length === 0) {
        setError('Seleziona almeno un cavo da collegare');
        return;
      }

      // Salva i dati di collegamento
      await comandeService.aggiornaDatiCollegamento(comanda.codice_comanda, datiCollegamento);
      
      onSuccess?.('Dati di collegamento salvati con successo');
      onClose();
      
    } catch (err) {
      console.error('Errore nel salvataggio:', err);
      setError(err.message || 'Errore nel salvataggio dei dati');
    } finally {
      setLoading(false);
    }
  };

  const getTipoComandaColor = (tipo) => {
    switch (tipo) {
      case 'COLLEGAMENTO_PARTENZA': return 'secondary';
      case 'COLLEGAMENTO_ARRIVO': return 'info';
      default: return 'default';
    }
  };

  const getTipoComandaLabel = (tipo) => {
    switch (tipo) {
      case 'COLLEGAMENTO_PARTENZA': return 'Coll. Partenza';
      case 'COLLEGAMENTO_ARRIVO': return 'Coll. Arrivo';
      default: return tipo;
    }
  };

  const getCollegamentoStatus = (cavo) => {
    const collegamenti = cavo.collegamenti || 0;
    const isPartenza = comanda.tipo_comanda === 'COLLEGAMENTO_PARTENZA';
    
    if (isPartenza) {
      return collegamenti & 1 ? 'Già collegato' : 'Da collegare';
    } else {
      return collegamenti & 2 ? 'Già collegato' : 'Da collegare';
    }
  };

  const getCollegamentoColor = (cavo) => {
    const status = getCollegamentoStatus(cavo);
    return status === 'Già collegato' ? 'success' : 'warning';
  };

  if (!comanda) return null;

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { minHeight: '70vh' }
      }}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={2}>
          <LinkIcon color="primary" />
          <Box>
            <Typography variant="h6">
              Gestione Collegamenti
            </Typography>
            <Box display="flex" alignItems="center" gap={1} mt={1}>
              <Typography variant="body2" color="textSecondary">
                Comanda: {comanda.codice_comanda}
              </Typography>
              <Chip 
                label={getTipoComandaLabel(comanda.tipo_comanda)}
                color={getTipoComandaColor(comanda.tipo_comanda)}
                size="small"
              />
              <Typography variant="body2" color="textSecondary">
                Responsabile: {comanda.responsabile}
              </Typography>
            </Box>
          </Box>
        </Box>
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2">
            <strong>Istruzioni:</strong> Seleziona i cavi che sono stati collegati fisicamente.
            {comanda.tipo_comanda === 'COLLEGAMENTO_PARTENZA' 
              ? ' Questa operazione registrerà il collegamento lato partenza.'
              : ' Questa operazione registrerà il collegamento lato arrivo.'
            }
          </Typography>
        </Alert>

        {loading ? (
          <Box display="flex" justifyContent="center" p={3}>
            <Typography>Caricamento cavi...</Typography>
          </Box>
        ) : (
          <TableContainer component={Paper} variant="outlined">
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Seleziona</TableCell>
                  <TableCell>ID Cavo</TableCell>
                  <TableCell>Tipologia</TableCell>
                  <TableCell>Formazione</TableCell>
                  <TableCell>Stato Collegamento</TableCell>
                  <TableCell>Stato Installazione</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {cavi.map((cavo) => (
                  <TableRow key={cavo.id_cavo}>
                    <TableCell>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={caviSelezionati[cavo.id_cavo] || false}
                            onChange={() => handleCavoToggle(cavo.id_cavo)}
                            disabled={getCollegamentoStatus(cavo) === 'Già collegato'}
                          />
                        }
                        label=""
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {cavo.id_cavo}
                      </Typography>
                    </TableCell>
                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>
                    <TableCell>{cavo.formazione || 'N/A'}</TableCell>
                    <TableCell>
                      <Chip 
                        label={getCollegamentoStatus(cavo)}
                        color={getCollegamentoColor(cavo)}
                        size="small"
                        icon={getCollegamentoStatus(cavo) === 'Già collegato' ? <CheckCircleIcon /> : <WarningIcon />}
                      />
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={cavo.stato_installazione || 'N/A'}
                        color={cavo.stato_installazione === 'Installato' ? 'success' : 'warning'}
                        size="small"
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}

        {cavi.length === 0 && !loading && (
          <Alert severity="info" sx={{ mt: 2 }}>
            Nessun cavo assegnato a questa comanda.
          </Alert>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 2, gap: 1 }}>
        <Button
          onClick={onClose}
          startIcon={<CancelIcon />}
          disabled={loading}
        >
          Annulla
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          startIcon={<SaveIcon />}
          disabled={loading || cavi.length === 0 || Object.values(caviSelezionati).every(v => !v)}
        >
          Salva Collegamenti
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CollegamentiDialog;
