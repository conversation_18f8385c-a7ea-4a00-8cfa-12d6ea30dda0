import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

import { useAuth } from './context/AuthContext';
import LoginPage from './pages/LoginPageNew';
import Dashboard from './pages/Dashboard';
import ProtectedRoute from './components/ProtectedRoute';

// Tema personalizzato con palette mariner
const theme = createTheme({
  palette: {
    primary: {
      main: '#1490fc', // mariner-600
      light: '#53caff', // mariner-400
      dark: '#125fbb', // mariner-800
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#123259', // mariner-950
      light: '#155293', // mariner-900
      dark: '#0d74e3', // mariner-700
      contrastText: '#ffffff',
    },
    info: {
      main: '#2bacff', // mariner-500
      light: '#8adeff', // mariner-300
      dark: '#0d74e3', // mariner-700
      contrastText: '#ffffff',
    },
    success: {
      main: '#2e7d32', // manteniamo il verde per successo
      light: '#4caf50',
      dark: '#1b5e20',
      contrastText: '#ffffff',
    },
    warning: {
      main: '#ed6c02', // manteniamo l'arancione per warning
      light: '#ff9800',
      dark: '#e65100',
      contrastText: '#ffffff',
    },
    error: {
      main: '#d32f2f', // manteniamo il rosso per errori
      light: '#f44336',
      dark: '#c62828',
      contrastText: '#ffffff',
    },
    background: {
      default: '#eefaff', // mariner-50
      paper: '#d8f3ff', // mariner-100
    },
  },
});

function App() {
  const { isAuthenticated, loading, user } = useAuth();

  console.log('App - Stato autenticazione:', { isAuthenticated, loading });

  // Se l'applicazione è in caricamento, mostra un indicatore di caricamento
  if (loading) {
    console.log('App - Mostrando indicatore di caricamento');
    return (
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
          <div style={{ textAlign: 'center' }}>
            <div>Caricamento...</div>
          </div>
        </div>
      </ThemeProvider>
    );
  }

  console.log('App - Rendering principale, isAuthenticated:', isAuthenticated);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Routes>
          <Route path="/login" element={
          isAuthenticated ? (
            user?.role === 'owner' ? <Navigate to="/dashboard/admin" replace /> :
            user?.role === 'user' ? <Navigate to="/dashboard/cantieri" replace /> :
            user?.role === 'cantieri_user' ? <Navigate to="/dashboard/cavi/visualizza" replace /> :
            <Navigate to="/dashboard" replace />
          ) : <LoginPage />
        } />
        <Route
          path="/dashboard/*"
          element={
            <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
          }
        />
        <Route
          path="/"
          element={
            isAuthenticated ? (
              user?.role === 'owner' ? <Navigate to="/dashboard/admin" replace /> :
              user?.role === 'user' ? <Navigate to="/dashboard/cantieri" replace /> :
              user?.role === 'cantieri_user' ? <Navigate to="/dashboard/cavi/visualizza" replace /> :
              <Navigate to="/dashboard" replace />
            ) : (
              <Navigate to="/login" replace />
            )
          }
        />
      </Routes>
    </ThemeProvider>
  );
}

export default App;
