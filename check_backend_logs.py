#!/usr/bin/env python3
"""
Verifica i log del backend per vedere cosa sta succedendo con la query
"""

import requests
import json

def check_backend():
    """Verifica il backend"""
    
    print("🔍 Verifica Backend - Comanda POS011")
    print("=" * 50)
    
    try:
        # Test connessione backend
        response = requests.get("http://localhost:8001/docs")
        if response.status_code == 200:
            print("✅ Backend raggiungibile su porta 8001")
        else:
            print(f"❌ Backend non raggiungibile: {response.status_code}")
            return
            
        # Test endpoint health (se esiste)
        try:
            response = requests.get("http://localhost:8001/health")
            if response.status_code == 200:
                print("✅ Backend health check OK")
        except:
            print("⚠️ Endpoint health non disponibile")
        
        print("\n📋 Per testare la query modificata:")
        print("1. Apri il browser su http://localhost:3000/dashboard/cavi/comande")
        print("2. Clicca su 'Inserimento Metri' per la comanda POS011")
        print("3. Verifica se il cavo C039 appare ancora nella lista")
        print("4. Se appare, la modifica alla query non è stata applicata")
        
        print("\n🔧 Per verificare i log del backend:")
        print("1. Guarda il terminale dove è in esecuzione il backend")
        print("2. Cerca i log che iniziano con '🔍 Processando cavo'")
        print("3. Verifica se C039 viene processato nonostante sia installato")
        
    except Exception as e:
        print(f"❌ Errore: {str(e)}")

if __name__ == "__main__":
    check_backend()
