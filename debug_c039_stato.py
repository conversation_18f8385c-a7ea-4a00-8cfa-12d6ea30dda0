#!/usr/bin/env python3
"""
Debug urgente per verificare lo stato esatto del cavo C039
"""

import psycopg2
from psycopg2.extras import RealDictCursor

def debug_c039_urgente():
    """Debug urgente del cavo C039"""
    
    print("🚨 DEBUG URGENTE - Cavo C039")
    print("=" * 50)
    
    try:
        # Connessione diretta al database
        conn = psycopg2.connect(
            host="localhost",
            database="cantieri",
            user="postgres",
            password="Taranto",
            cursor_factory=RealDictCursor
        )
        
        with conn.cursor() as c:
            
            # 1. Stato esatto del cavo C039
            print("\n📋 1. STATO ESATTO CAVO C039:")
            c.execute("""
                SELECT 
                    id_cavo,
                    stato_installazione,
                    metratura_reale,
                    comanda_posa,
                    id_bobina,
                    id_cantiere
                FROM cavi 
                WHERE id_cavo = 'C039'
            """)
            
            cavo = c.fetchone()
            if cavo:
                print(f"   ID Cavo: {cavo['id_cavo']}")
                print(f"   Stato: '{cavo['stato_installazione']}'")
                print(f"   Metri Reali: {cavo['metratura_reale']}")
                print(f"   Comanda Posa: {cavo['comanda_posa']}")
                print(f"   ID Bobina: {cavo['id_bobina']}")
                print(f"   ID Cantiere: {cavo['id_cantiere']}")
                
                # Verifica byte per byte
                stato = cavo['stato_installazione']
                print(f"   Stato (bytes): {stato.encode() if stato else 'NULL'}")
                print(f"   Lunghezza stato: {len(stato) if stato else 0}")
                
            else:
                print("   ❌ Cavo C039 non trovato!")
                return
            
            # 2. Test filtro diretto
            print("\n📋 2. TEST FILTRO DIRETTO:")
            
            # Test 1: != 'Installato'
            c.execute("""
                SELECT COUNT(*) as count
                FROM cavi 
                WHERE id_cavo = 'C039' AND stato_installazione != 'Installato'
            """)
            count1 = c.fetchone()['count']
            print(f"   stato_installazione != 'Installato': {count1}")
            
            # Test 2: = 'Installato'
            c.execute("""
                SELECT COUNT(*) as count
                FROM cavi 
                WHERE id_cavo = 'C039' AND stato_installazione = 'Installato'
            """)
            count2 = c.fetchone()['count']
            print(f"   stato_installazione = 'Installato': {count2}")
            
            # Test 3: UPPER
            c.execute("""
                SELECT COUNT(*) as count
                FROM cavi 
                WHERE id_cavo = 'C039' AND UPPER(stato_installazione) = 'INSTALLATO'
            """)
            count3 = c.fetchone()['count']
            print(f"   UPPER(stato_installazione) = 'INSTALLATO': {count3}")
            
            # Test 4: TRIM
            c.execute("""
                SELECT COUNT(*) as count
                FROM cavi 
                WHERE id_cavo = 'C039' AND TRIM(stato_installazione) = 'Installato'
            """)
            count4 = c.fetchone()['count']
            print(f"   TRIM(stato_installazione) = 'Installato': {count4}")
            
            # 3. Tutti i valori possibili di stato_installazione
            print("\n📋 3. TUTTI I VALORI DI STATO_INSTALLAZIONE:")
            c.execute("""
                SELECT DISTINCT stato_installazione, COUNT(*) as count
                FROM cavi 
                GROUP BY stato_installazione
                ORDER BY count DESC
            """)
            
            stati = c.fetchall()
            for stato in stati:
                val = stato['stato_installazione']
                print(f"   '{val}' (len={len(val) if val else 0}): {stato['count']} cavi")
            
            # 4. Query comanda POS011
            print("\n📋 4. QUERY COMANDA POS011:")
            c.execute("""
                SELECT id_cavo, stato_installazione, comanda_posa
                FROM cavi
                WHERE comanda_posa = 'POS011'
                ORDER BY id_cavo
            """)
            
            cavi_pos011 = c.fetchall()
            print(f"   Cavi in POS011: {len(cavi_pos011)}")
            for cavo in cavi_pos011:
                print(f"   - {cavo['id_cavo']}: '{cavo['stato_installazione']}'")
            
    except Exception as e:
        print(f"❌ Errore: {str(e)}")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    debug_c039_urgente()
