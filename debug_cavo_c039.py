#!/usr/bin/env python3
"""
Debug specifico per il cavo C039 che appare ancora nella comanda POS011
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from modules.database_pg import database_connection

def debug_cavo_c039():
    """Debug del cavo C039 nella comanda POS011"""
    
    print("🔍 Debug Cavo C039 - Comanda POS011")
    print("=" * 50)
    
    try:
        with database_connection() as (conn, c):
            
            # 1. Verifica stato del cavo C039
            print("\n📋 1. STATO CAVO C039:")
            c.execute("""
                SELECT
                    id_cavo,
                    stato_installazione,
                    metratura_reale,
                    comanda_posa,
                    id_bobina
                FROM Cavi
                WHERE id_cavo = 'C039'
            """)
            
            cavo = c.fetchone()
            if cavo:
                print(f"   ID Cavo: {cavo['id_cavo']}")
                print(f"   Stato: {cavo['stato_installazione']}")
                print(f"   Metri Reali: {cavo['metratura_reale']}")
                print(f"   Comanda Posa: {cavo['comanda_posa']}")

                print(f"   ID Bobina: {cavo['id_bobina']}")
            else:
                print("   ❌ Cavo C039 non trovato!")
                return
            
            # 2. Verifica comanda POS011
            print("\n📋 2. DETTAGLI COMANDA POS011:")
            c.execute("""
                SELECT 
                    codice_comanda,
                    tipo_comanda,
                    stato,
                    responsabile,
                    id_cantiere
                FROM Comande 
                WHERE codice_comanda = 'POS011'
            """)
            
            comanda = c.fetchone()
            if comanda:
                print(f"   Codice: {comanda['codice_comanda']}")
                print(f"   Tipo: {comanda['tipo_comanda']}")
                print(f"   Stato: {comanda['stato']}")
                print(f"   Responsabile: {comanda['responsabile']}")
                print(f"   ID Cantiere: {comanda['id_cantiere']}")
            else:
                print("   ❌ Comanda POS011 non trovata!")
                return
            
            # 3. Test della query originale (senza filtro)
            print("\n📋 3. QUERY ORIGINALE (senza filtro):")
            c.execute("""
                SELECT id_cavo, stato_installazione, comanda_posa
                FROM Cavi
                WHERE id_cantiere = %s AND comanda_posa = %s
                ORDER BY id_cavo
            """, (comanda['id_cantiere'], 'POS011'))
            
            cavi_originale = c.fetchall()
            print(f"   Cavi trovati: {len(cavi_originale)}")
            for cavo in cavi_originale:
                print(f"   - {cavo['id_cavo']}: {cavo['stato_installazione']} (comanda: {cavo['comanda_posa']})")
            
            # 4. Test della query con filtro
            print("\n📋 4. QUERY CON FILTRO (stato != Installato):")
            c.execute("""
                SELECT id_cavo, stato_installazione, comanda_posa
                FROM Cavi
                WHERE id_cantiere = %s AND comanda_posa = %s AND stato_installazione != 'Installato'
                ORDER BY id_cavo
            """, (comanda['id_cantiere'], 'POS011'))
            
            cavi_filtrati = c.fetchall()
            print(f"   Cavi trovati: {len(cavi_filtrati)}")
            for cavo in cavi_filtrati:
                print(f"   - {cavo['id_cavo']}: {cavo['stato_installazione']} (comanda: {cavo['comanda_posa']})")
            
            # 5. Verifica se C039 è nel risultato filtrato
            c039_in_filtrati = any(c['id_cavo'] == 'C039' for c in cavi_filtrati)
            print(f"\n📋 5. C039 NEL RISULTATO FILTRATO: {'SÌ' if c039_in_filtrati else 'NO'}")
            
            # 6. Analisi del problema
            print("\n📋 6. ANALISI:")
            if cavo['stato_installazione'] == 'Installato' and c039_in_filtrati:
                print("   ❌ PROBLEMA: Cavo installato ma ancora nel risultato filtrato!")
                print("   🔧 CAUSA POSSIBILE: Query non applicata correttamente")
            elif cavo['stato_installazione'] != 'Installato' and c039_in_filtrati:
                print("   ⚠️ NORMALE: Cavo non installato, correttamente nel risultato")
            elif cavo['stato_installazione'] == 'Installato' and not c039_in_filtrati:
                print("   ✅ CORRETTO: Cavo installato, correttamente escluso dal risultato")
            else:
                print("   🤔 SITUAZIONE ANOMALA: Verificare manualmente")
            
            # 7. Verifica valori esatti per case sensitivity
            print(f"\n📋 7. VERIFICA CASE SENSITIVITY:")
            print(f"   Stato esatto: '{cavo['stato_installazione']}'")
            print(f"   Lunghezza: {len(cavo['stato_installazione'])}")
            print(f"   Bytes: {cavo['stato_installazione'].encode()}")
            
            # Test con UPPER/LOWER
            c.execute("""
                SELECT COUNT(*) as count
                FROM Cavi
                WHERE id_cantiere = %s AND comanda_posa = %s 
                AND UPPER(stato_installazione) != UPPER('Installato')
            """, (comanda['id_cantiere'], 'POS011'))
            
            count_upper = c.fetchone()['count']
            print(f"   Query con UPPER: {count_upper} cavi")
            
    except Exception as e:
        print(f"❌ Errore nel debug: {str(e)}")

if __name__ == "__main__":
    debug_cavo_c039()
