/* Variabili CSS per la palette mariner */
:root {
  /* Pa<PERSON> mariner */
  --mariner-50: #eefaff;
  --mariner-100: #d8f3ff;
  --mariner-200: #bae9ff;
  --mariner-300: #8adeff;
  --mariner-400: #53caff;
  --mariner-500: #2bacff;
  --mariner-600: #1490fc;
  --mariner-700: #0d74e3;
  --mariner-800: #125fbb;
  --mariner-900: #155293;
  --mariner-950: #123259;

  /* Colori del sistema */
  --primary-color: var(--mariner-600);
  --primary-light: var(--mariner-400);
  --primary-dark: var(--mariner-800);
  --secondary-color: var(--mariner-950);
  --secondary-light: var(--mariner-900);
  --secondary-dark: var(--mariner-700);

  /* Colori di sfondo */
  --background-color: var(--mariner-50);
  --surface-color: var(--mariner-100);
  --card-background: #ffffff;

  /* Colori di testo */
  --text-primary: #212121;
  --text-secondary: #757575;
  --text-on-primary: #ffffff;

  /* Colori di stato */
  --success-color: #2e7d32;
  --warning-color: #ed6c02;
  --error-color: #d32f2f;

  /* Colori di interazione */
  --rest-state: #f5f7fa;
  --hover-state: rgba(20, 144, 252, 0.1); /* mariner-600 con opacità */
  --border-color: #e0e0e0;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow-x: hidden; /* Previene scrollbar orizzontale a livello globale */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background-color);
  color: var(--text-primary);
}

#root {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}
