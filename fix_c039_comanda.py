#!/usr/bin/env python3
"""
Fix urgente per rimuovere il cavo C039 dalla comanda POS011
"""

import psycopg2
from psycopg2.extras import RealDictCursor

def fix_c039_comanda():
    """Fix del cavo C039 che è installato ma ancora in comanda"""
    
    print("🔧 FIX URGENTE - Rimozione C039 da POS011")
    print("=" * 50)
    
    try:
        # Connessione diretta al database
        conn = psycopg2.connect(
            host="localhost",
            database="cantieri",
            user="postgres",
            password="Taranto",
            cursor_factory=RealDictCursor
        )
        
        with conn.cursor() as c:
            
            # 1. Verifica stato attuale
            print("\n📋 1. STATO ATTUALE:")
            c.execute("""
                SELECT id_cavo, stato_installazione, metratura_reale, comanda_posa
                FROM cavi 
                WHERE id_cavo = 'C039'
            """)
            
            cavo = c.fetchone()
            if cavo:
                print(f"   C039: stato='{cavo['stato_installazione']}', metri={cavo['metratura_reale']}, comanda='{cavo['comanda_posa']}'")
            else:
                print("   ❌ Cavo C039 non trovato!")
                return
            
            # 2. Fix: Rimuovi dalla comanda se installato
            if cavo['stato_installazione'] == 'Installato' and cavo['comanda_posa'] == 'POS011':
                print("\n🔧 2. APPLICANDO FIX:")
                print("   Rimuovendo C039 dalla comanda POS011...")
                
                c.execute("""
                    UPDATE cavi 
                    SET comanda_posa = NULL 
                    WHERE id_cavo = 'C039' AND stato_installazione = 'Installato'
                """)
                
                rows_affected = c.rowcount
                print(f"   Righe aggiornate: {rows_affected}")
                
                if rows_affected > 0:
                    conn.commit()
                    print("   ✅ Fix applicato con successo!")
                else:
                    print("   ⚠️ Nessuna riga aggiornata")
                    
            else:
                print("\n⚠️ 2. NESSUN FIX NECESSARIO:")
                print("   Il cavo non è installato o non è in POS011")
            
            # 3. Verifica stato finale
            print("\n📋 3. STATO FINALE:")
            c.execute("""
                SELECT id_cavo, stato_installazione, metratura_reale, comanda_posa
                FROM cavi 
                WHERE id_cavo = 'C039'
            """)
            
            cavo_finale = c.fetchone()
            if cavo_finale:
                print(f"   C039: stato='{cavo_finale['stato_installazione']}', metri={cavo_finale['metratura_reale']}, comanda='{cavo_finale['comanda_posa']}'")
                
                if cavo_finale['stato_installazione'] == 'Installato' and cavo_finale['comanda_posa'] is None:
                    print("   ✅ CORRETTO: Cavo installato e rimosso dalla comanda")
                elif cavo_finale['stato_installazione'] == 'Installato' and cavo_finale['comanda_posa'] is not None:
                    print("   ❌ ERRORE: Cavo installato ma ancora in comanda!")
                else:
                    print("   ⚠️ STATO ANOMALO: Verificare manualmente")
            
            # 4. Verifica comanda POS011 dopo il fix
            print("\n📋 4. CAVI RIMANENTI IN POS011:")
            c.execute("""
                SELECT id_cavo, stato_installazione
                FROM cavi
                WHERE comanda_posa = 'POS011'
                ORDER BY id_cavo
            """)
            
            cavi_rimanenti = c.fetchall()
            print(f"   Cavi rimanenti: {len(cavi_rimanenti)}")
            for cavo in cavi_rimanenti:
                print(f"   - {cavo['id_cavo']}: {cavo['stato_installazione']}")
            
            if not any(c['id_cavo'] == 'C039' for c in cavi_rimanenti):
                print("   ✅ C039 non è più nella comanda POS011!")
            else:
                print("   ❌ C039 è ancora nella comanda POS011!")
                
    except Exception as e:
        print(f"❌ Errore: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    fix_c039_comanda()
