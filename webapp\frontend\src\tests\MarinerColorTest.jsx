import React from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON>, 
  Card, 
  CardContent, 
  Typography, 
  Paper,
  Chip,
  Alert
} from '@mui/material';
import { useTheme } from '@mui/material/styles';

/**
 * Componente di test per verificare l'implementazione della palette mariner
 * Questo componente mostra tutti i colori della palette e come vengono utilizzati
 */
const MarinerColorTest = () => {
  const theme = useTheme();

  const marinerColors = [
    { name: 'mariner-50', value: '#eefaff', description: 'Sfondo principale' },
    { name: 'mariner-100', value: '#d8f3ff', description: 'Superfici secondarie' },
    { name: 'mariner-200', value: '#bae9ff', description: 'Bordi e divisori' },
    { name: 'mariner-300', value: '#8adeff', description: 'Elementi disabilitati' },
    { name: 'mariner-400', value: '#53caff', description: 'Colore primario chiaro' },
    { name: 'mariner-500', value: '#2bacff', description: 'Colore informativo' },
    { name: 'mariner-600', value: '#1490fc', description: 'Colore primario principale' },
    { name: 'mariner-700', value: '#0d74e3', description: 'Colore primario scuro' },
    { name: 'mariner-800', value: '#125fbb', description: 'Colore primario molto scuro' },
    { name: 'mariner-900', value: '#155293', description: 'Colore secondario chiaro' },
    { name: 'mariner-950', value: '#123259', description: 'Colore secondario principale' },
  ];

  return (
    <Box sx={{ p: 3, backgroundColor: 'background.default' }}>
      <Typography variant="h4" gutterBottom color="primary">
        Test Palette Mariner
      </Typography>
      
      <Typography variant="body1" sx={{ mb: 3 }}>
        Questo componente verifica l'implementazione corretta della palette di colori mariner.
      </Typography>

      {/* Palette di colori */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Palette Completa
        </Typography>
        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 1 }}>
          {marinerColors.map((color) => (
            <Box
              key={color.name}
              sx={{
                backgroundColor: color.value,
                p: 2,
                borderRadius: 1,
                border: '1px solid #e0e0e0',
                textAlign: 'center'
              }}
            >
              <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block' }}>
                {color.name}
              </Typography>
              <Typography variant="caption" sx={{ display: 'block', opacity: 0.8 }}>
                {color.value}
              </Typography>
              <Typography variant="caption" sx={{ display: 'block', fontSize: '0.7rem' }}>
                {color.description}
              </Typography>
            </Box>
          ))}
        </Box>
      </Paper>

      {/* Test componenti Material-UI */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Componenti Material-UI
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mb: 2 }}>
          <Button variant="contained" color="primary">
            Primario
          </Button>
          <Button variant="outlined" color="primary">
            Primario Outlined
          </Button>
          <Button variant="contained" color="secondary">
            Secondario
          </Button>
          <Button variant="outlined" color="secondary">
            Secondario Outlined
          </Button>
          <Button variant="contained" color="info">
            Info
          </Button>
        </Box>
        
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
          <Chip label="Chip Primario" color="primary" />
          <Chip label="Chip Secondario" color="secondary" />
          <Chip label="Chip Info" color="info" />
          <Chip label="Chip Success" color="success" />
        </Box>

        <Alert severity="info" sx={{ mb: 1 }}>
          Alert informativo con colori mariner
        </Alert>
        <Alert severity="success" sx={{ mb: 1 }}>
          Alert di successo
        </Alert>
        <Alert severity="warning" sx={{ mb: 1 }}>
          Alert di warning
        </Alert>
        <Alert severity="error">
          Alert di errore
        </Alert>
      </Paper>

      {/* Test variabili CSS */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Variabili CSS
        </Typography>
        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: 2 }}>
          <Box 
            sx={{ 
              backgroundColor: 'var(--primary-color)', 
              color: 'white', 
              p: 2, 
              borderRadius: 1,
              textAlign: 'center'
            }}
          >
            --primary-color
          </Box>
          <Box 
            sx={{ 
              backgroundColor: 'var(--primary-light)', 
              color: 'white', 
              p: 2, 
              borderRadius: 1,
              textAlign: 'center'
            }}
          >
            --primary-light
          </Box>
          <Box 
            sx={{ 
              backgroundColor: 'var(--primary-dark)', 
              color: 'white', 
              p: 2, 
              borderRadius: 1,
              textAlign: 'center'
            }}
          >
            --primary-dark
          </Box>
          <Box 
            sx={{ 
              backgroundColor: 'var(--secondary-color)', 
              color: 'white', 
              p: 2, 
              borderRadius: 1,
              textAlign: 'center'
            }}
          >
            --secondary-color
          </Box>
        </Box>
      </Paper>

      {/* Test stati di hover */}
      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Stati di Interazione
        </Typography>
        <Box
          sx={{
            p: 2,
            backgroundColor: 'var(--rest-state)',
            borderRadius: 1,
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            '&:hover': {
              backgroundColor: 'var(--hover-state)',
              borderColor: 'var(--primary-color)',
              border: '1px solid'
            }
          }}
        >
          <Typography>
            Elemento con stato hover (passa il mouse sopra)
          </Typography>
        </Box>
      </Paper>

      {/* Informazioni tema */}
      <Paper sx={{ p: 2, mt: 3, backgroundColor: 'background.paper' }}>
        <Typography variant="h6" gutterBottom>
          Informazioni Tema Corrente
        </Typography>
        <Typography variant="body2" component="pre" sx={{ fontSize: '0.8rem' }}>
          {JSON.stringify({
            'primary.main': theme.palette.primary.main,
            'primary.light': theme.palette.primary.light,
            'primary.dark': theme.palette.primary.dark,
            'secondary.main': theme.palette.secondary.main,
            'background.default': theme.palette.background.default,
            'background.paper': theme.palette.background.paper,
          }, null, 2)}
        </Typography>
      </Paper>
    </Box>
  );
};

export default MarinerColorTest;
