# Palette di Colori Mariner - Sistema CMS

## Panoramica

Il sistema CMS utilizza ora la palette di colori "mariner" per garantire coerenza visiva e un'esperienza utente professionale. Questa palette è stata implementata in tutto il sistema attraverso Material-UI, Tailwind CSS e variabili CSS personalizzate.

## Palette Mariner

```css
mariner-50:  #eefaff  /* Sfondo principale */
mariner-100: #d8f3ff  /* Superfici secondarie */
mariner-200: #bae9ff  /* Bordi e divisori */
mariner-300: #8adeff  /* Elementi disabilitati */
mariner-400: #53caff  /* Colore primario chiaro */
mariner-500: #2bacff  /* Colore informativo */
mariner-600: #1490fc  /* Colore primario principale */
mariner-700: #0d74e3  /* Colore primario scuro */
mariner-800: #125fbb  /* Colore primario molto scuro */
mariner-900: #155293  /* Colore secondario chiaro */
mariner-950: #123259  /* Colore secondario principale */
```

## Utilizzo nei Componenti

### Material-UI
I componenti Material-UI utilizzano automaticamente la palette attraverso il tema personalizzato:

```javascript
// Colori principali
theme.palette.primary.main    // #1490fc (mariner-600)
theme.palette.primary.light   // #53caff (mariner-400)
theme.palette.primary.dark    // #125fbb (mariner-800)

// Colori secondari
theme.palette.secondary.main  // #123259 (mariner-950)
theme.palette.secondary.light // #155293 (mariner-900)
theme.palette.secondary.dark  // #0d74e3 (mariner-700)

// Sfondi
theme.palette.background.default // #eefaff (mariner-50)
theme.palette.background.paper   // #d8f3ff (mariner-100)
```

### Tailwind CSS
Utilizza le classi Tailwind con la palette mariner:

```html
<!-- Colori di sfondo -->
<div class="bg-mariner-50">Sfondo principale</div>
<div class="bg-mariner-100">Superficie secondaria</div>

<!-- Colori di testo -->
<p class="text-mariner-600">Testo primario</p>
<p class="text-mariner-800">Testo scuro</p>

<!-- Bordi -->
<div class="border-mariner-200">Bordo chiaro</div>
<div class="border-mariner-600">Bordo primario</div>
```

### Variabili CSS
Utilizza le variabili CSS personalizzate:

```css
/* Colori principali */
color: var(--primary-color);        /* mariner-600 */
color: var(--primary-light);        /* mariner-400 */
color: var(--primary-dark);         /* mariner-800 */

/* Colori di sfondo */
background-color: var(--background-color); /* mariner-50 */
background-color: var(--surface-color);    /* mariner-100 */

/* Stati di interazione */
background-color: var(--rest-state);  /* #f5f7fa */
background-color: var(--hover-state); /* rgba(20, 144, 252, 0.1) */
```

## Linee Guida per l'Uso

### Gerarchia dei Colori
1. **mariner-50**: Sfondo principale dell'applicazione
2. **mariner-100**: Superfici e card
3. **mariner-200-300**: Bordi e divisori
4. **mariner-400-500**: Elementi informativi e accenti
5. **mariner-600**: Colore primario per azioni principali
6. **mariner-700-800**: Varianti scure del primario
7. **mariner-900-950**: Colori secondari per contrasto

### Accessibilità
- Utilizzare sempre combinazioni di colori con contrasto sufficiente
- mariner-600 su bianco: contrasto 4.5:1 (WCAG AA)
- mariner-800 su mariner-50: contrasto 7:1 (WCAG AAA)

### Esempi di Utilizzo

#### Pulsanti
```jsx
// Pulsante primario
<Button variant="contained" color="primary">
  Azione Principale
</Button>

// Pulsante secondario
<Button variant="outlined" color="primary">
  Azione Secondaria
</Button>
```

#### Card e Superfici
```jsx
<Paper sx={{ 
  backgroundColor: 'background.paper', // mariner-100
  border: '1px solid',
  borderColor: 'mariner.200'
}}>
  Contenuto della card
</Paper>
```

#### Stati di Hover
```css
.interactive-element:hover {
  background-color: var(--hover-state);
  border-color: var(--primary-color);
}
```

## Migrazione da Colori Precedenti

I seguenti colori sono stati sostituiti:
- `#1976d2` → `#1490fc` (mariner-600)
- `#42a5f5` → `#53caff` (mariner-400)
- `#0d47a1` → `#125fbb` (mariner-800)
- `#f5f5f5` → `#eefaff` (mariner-50)

## Note per gli Sviluppatori

1. **Preferire le variabili CSS** per la massima flessibilità
2. **Utilizzare il tema Material-UI** per i componenti MUI
3. **Usare le classi Tailwind** per utility rapide
4. **Mantenere la coerenza** utilizzando sempre la stessa palette
5. **Testare l'accessibilità** con strumenti di contrasto

## File Modificati

- `src/App.js` - Tema Material-UI aggiornato
- `src/index.css` - Variabili CSS aggiunte
- `tailwind.config.js` - Configurazione Tailwind
- `public/manifest.json` - Colori del tema app
- `src/styles/reports.css` - Stili dei report aggiornati
